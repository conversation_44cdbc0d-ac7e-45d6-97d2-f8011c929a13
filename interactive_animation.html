<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>炫酷全屏交互动画</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            overflow: hidden;
            background: linear-gradient(45deg, #000000, #0f0f23, #1a0033, #330066);
            cursor: none;
            animation: backgroundShift 10s ease-in-out infinite alternate;
        }

        @keyframes backgroundShift {
            0% { background: linear-gradient(45deg, #000000, #0f0f23, #1a0033, #330066); }
            50% { background: linear-gradient(135deg, #001122, #003366, #0066cc, #0099ff); }
            100% { background: linear-gradient(225deg, #220011, #660033, #cc0066, #ff0099); }
        }

        #canvas {
            display: block;
            background: radial-gradient(circle at center, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.8));
            filter: contrast(1.2) brightness(1.1);
        }

        .fullscreen-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-family: 'Arial', sans-serif;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .fullscreen-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.05);
        }

        .cursor {
            position: fixed;
            width: 30px;
            height: 30px;
            border: 3px solid #00ffff;
            border-radius: 50%;
            pointer-events: none;
            z-index: 999;
            transition: all 0.05s ease;
            box-shadow:
                0 0 30px #00ffff,
                inset 0 0 30px #00ffff,
                0 0 60px rgba(0, 255, 255, 0.5),
                0 0 100px rgba(0, 255, 255, 0.3);
            animation: cursorPulse 2s ease-in-out infinite;
        }

        @keyframes cursorPulse {
            0%, 100% { transform: scale(1); opacity: 0.8; }
            50% { transform: scale(1.2); opacity: 1; }
        }

        .info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            color: rgba(255, 255, 255, 0.9);
            font-family: 'Arial', sans-serif;
            font-size: 16px;
            z-index: 1000;
            text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
            background: rgba(0, 0, 0, 0.3);
            padding: 10px 15px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .particle-counter {
            position: fixed;
            top: 20px;
            left: 20px;
            color: #00ffff;
            font-family: 'Arial', sans-serif;
            font-size: 18px;
            font-weight: bold;
            z-index: 1000;
            text-shadow: 0 0 15px #00ffff;
            background: rgba(0, 0, 0, 0.5);
            padding: 10px 15px;
            border-radius: 10px;
            border: 2px solid rgba(0, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body>
    <canvas id="canvas"></canvas>
    <div class="cursor" id="cursor"></div>
    <button class="fullscreen-btn" onclick="toggleFullscreen()">全屏</button>
    <div class="particle-counter" id="particleCounter">粒子数量: 0</div>
    <div class="info">
        🖱️ 移动鼠标跟随 | 🎯 单击爆炸 | 💥 双击超级爆炸 | 🎡 滚轮调节粒子倍率<br>
        🌟 长按连续爆炸 | ⌨️ 空格键全屏爆炸 | 🔄 C键清屏 | 🌀 S键螺旋 | 🔄 R键重置
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const cursor = document.getElementById('cursor');
        const particleCounter = document.getElementById('particleCounter');

        let particles = [];
        let mouse = { x: 0, y: 0 };
        let animationId;
        let particleMultiplier = 1;
        let isMouseDown = false;
        let mouseDownTimer = null;
        let backgroundHue = 0;
        let waveOffset = 0;

        // 设置画布大小
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }

        // 增强粒子类
        class Particle {
            constructor(x, y, vx = 0, vy = 0, life = 100, type = 'normal') {
                this.x = x;
                this.y = y;
                this.vx = vx;
                this.vy = vy;
                this.life = life;
                this.maxLife = life;
                this.size = Math.random() * 4 + 1;
                this.color = this.getRandomColor();
                this.trail = [];
                this.type = type;
                this.rotation = Math.random() * Math.PI * 2;
                this.rotationSpeed = (Math.random() - 0.5) * 0.2;
                this.pulsePhase = Math.random() * Math.PI * 2;
                this.gravity = type === 'explosion' ? 0.1 : 0.05;
                this.magnetism = Math.random() * 0.3 + 0.1;
            }

            getRandomColor() {
                const hue = (backgroundHue + Math.random() * 120) % 360;
                const saturation = 80 + Math.random() * 20;
                const lightness = 50 + Math.random() * 30;
                return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
            }

            update() {
                // 添加轨迹点
                this.trail.push({ x: this.x, y: this.y, life: this.life });
                if (this.trail.length > 15) {
                    this.trail.shift();
                }

                // 更新旋转和脉冲
                this.rotation += this.rotationSpeed;
                this.pulsePhase += 0.1;

                // 受鼠标影响 - 增强磁力效果
                const dx = mouse.x - this.x;
                const dy = mouse.y - this.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < 200) {
                    const force = (200 - distance) / 200 * this.magnetism;
                    this.vx += (dx / distance) * force * 0.8;
                    this.vy += (dy / distance) * force * 0.8;
                }

                // 粒子间相互作用
                particles.forEach(other => {
                    if (other !== this) {
                        const dx2 = other.x - this.x;
                        const dy2 = other.y - this.y;
                        const dist2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);
                        if (dist2 < 50 && dist2 > 0) {
                            const force = (50 - dist2) / 50 * 0.02;
                            this.vx += (dx2 / dist2) * force;
                            this.vy += (dy2 / dist2) * force;
                        }
                    }
                });

                // 重力效果
                this.vy += this.gravity;

                // 更新位置
                this.x += this.vx;
                this.y += this.vy;

                // 添加阻力
                this.vx *= 0.995;
                this.vy *= 0.995;

                // 边界反弹 - 增强效果
                if (this.x < 0 || this.x > canvas.width) {
                    this.vx *= -0.9;
                    this.x = Math.max(0, Math.min(canvas.width, this.x));
                }
                if (this.y < 0 || this.y > canvas.height) {
                    this.vy *= -0.9;
                    this.y = Math.max(0, Math.min(canvas.height, this.y));
                }

                // 生命值递减
                this.life--;
            }

            draw() {
                const alpha = this.life / this.maxLife;
                const pulseSize = 1 + Math.sin(this.pulsePhase) * 0.3;

                // 绘制增强轨迹
                ctx.save();
                ctx.globalCompositeOperation = 'screen';
                for (let i = 0; i < this.trail.length - 1; i++) {
                    const current = this.trail[i];
                    const next = this.trail[i + 1];
                    const trailAlpha = (i / this.trail.length) * alpha * 0.8;

                    ctx.strokeStyle = this.color.replace('hsl', 'hsla').replace(')', `, ${trailAlpha})`);
                    ctx.lineWidth = (this.size * trailAlpha) + 1;
                    ctx.lineCap = 'round';

                    ctx.beginPath();
                    ctx.moveTo(current.x, current.y);
                    ctx.lineTo(next.x, next.y);
                    ctx.stroke();
                }
                ctx.restore();

                // 绘制主粒子 - 多层效果
                ctx.save();
                ctx.globalAlpha = alpha;
                ctx.translate(this.x, this.y);
                ctx.rotate(this.rotation);

                // 外层光晕
                ctx.shadowBlur = 40;
                ctx.shadowColor = this.color;
                ctx.fillStyle = this.color.replace('hsl', 'hsla').replace(')', ', 0.3)');
                ctx.beginPath();
                ctx.arc(0, 0, this.size * pulseSize * 2, 0, Math.PI * 2);
                ctx.fill();

                // 中层
                ctx.shadowBlur = 20;
                ctx.fillStyle = this.color.replace('hsl', 'hsla').replace(')', ', 0.7)');
                ctx.beginPath();
                ctx.arc(0, 0, this.size * pulseSize * 1.5, 0, Math.PI * 2);
                ctx.fill();

                // 核心
                ctx.shadowBlur = 10;
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(0, 0, this.size * pulseSize, 0, Math.PI * 2);
                ctx.fill();

                // 添加星形闪烁效果
                if (this.type === 'explosion' && alpha > 0.5) {
                    ctx.strokeStyle = '#ffffff';
                    ctx.lineWidth = 2;
                    ctx.shadowBlur = 15;
                    ctx.shadowColor = '#ffffff';
                    for (let i = 0; i < 4; i++) {
                        ctx.beginPath();
                        ctx.moveTo(-this.size * 2, 0);
                        ctx.lineTo(this.size * 2, 0);
                        ctx.stroke();
                        ctx.rotate(Math.PI / 4);
                    }
                }

                ctx.restore();
            }

            isDead() {
                return this.life <= 0;
            }
        }

        // 创建增强粒子
        function createParticles(x, y, count = 5) {
            const adjustedCount = Math.floor(count * particleMultiplier);
            for (let i = 0; i < adjustedCount; i++) {
                const angle = (Math.PI * 2 * i) / adjustedCount + Math.random() * 0.5;
                const speed = Math.random() * 4 + 1;
                const vx = Math.cos(angle) * speed;
                const vy = Math.sin(angle) * speed;
                particles.push(new Particle(x, y, vx, vy, 200 + Math.random() * 150, 'normal'));
            }
        }

        // 创建超级爆炸效果
        function createExplosion(x, y, intensity = 1) {
            const baseCount = 50 * intensity;
            const adjustedCount = Math.floor(baseCount * particleMultiplier);

            // 主爆炸
            for (let i = 0; i < adjustedCount; i++) {
                const angle = Math.random() * Math.PI * 2;
                const speed = Math.random() * 12 + 3;
                const vx = Math.cos(angle) * speed;
                const vy = Math.sin(angle) * speed;
                particles.push(new Particle(x, y, vx, vy, 150 + Math.random() * 100, 'explosion'));
            }

            // 冲击波效果
            for (let i = 0; i < 20; i++) {
                const angle = (Math.PI * 2 * i) / 20;
                const speed = 15 + Math.random() * 5;
                const vx = Math.cos(angle) * speed;
                const vy = Math.sin(angle) * speed;
                particles.push(new Particle(x, y, vx, vy, 80, 'explosion'));
            }
        }

        // 创建螺旋效果
        function createSpiral(x, y) {
            for (let i = 0; i < 30 * particleMultiplier; i++) {
                const angle = (i * 0.5) + waveOffset;
                const radius = i * 2;
                const px = x + Math.cos(angle) * radius;
                const py = y + Math.sin(angle) * radius;
                const vx = Math.cos(angle + Math.PI/2) * 2;
                const vy = Math.sin(angle + Math.PI/2) * 2;
                particles.push(new Particle(px, py, vx, vy, 120, 'normal'));
            }
        }

        // 增强动画循环
        function animate() {
            // 动态背景
            backgroundHue = (backgroundHue + 0.5) % 360;
            waveOffset += 0.05;

            // 渐变清除效果
            const gradient = ctx.createRadialGradient(
                canvas.width/2, canvas.height/2, 0,
                canvas.width/2, canvas.height/2, Math.max(canvas.width, canvas.height)
            );
            gradient.addColorStop(0, 'rgba(0, 0, 0, 0.05)');
            gradient.addColorStop(1, 'rgba(0, 0, 0, 0.15)');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制背景波纹
            drawBackgroundWaves();

            // 更新和绘制粒子
            particles = particles.filter(particle => {
                particle.update();
                particle.draw();
                return !particle.isDead();
            });

            // 更新粒子计数器
            particleCounter.textContent = `粒子数量: ${particles.length} | 倍率: ${particleMultiplier.toFixed(1)}x`;

            // 智能粒子生成
            const targetParticles = 50 * particleMultiplier;
            if (particles.length < targetParticles && Math.random() < 0.4) {
                createParticles(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    Math.random() < 0.1 ? 3 : 1
                );
            }

            animationId = requestAnimationFrame(animate);
        }

        // 绘制背景波纹
        function drawBackgroundWaves() {
            ctx.save();
            ctx.globalCompositeOperation = 'screen';
            ctx.globalAlpha = 0.1;

            for (let i = 0; i < 3; i++) {
                ctx.strokeStyle = `hsl(${backgroundHue + i * 60}, 50%, 30%)`;
                ctx.lineWidth = 2;
                ctx.beginPath();

                for (let x = 0; x <= canvas.width; x += 10) {
                    const y = canvas.height/2 + Math.sin((x + waveOffset * 100 + i * 100) * 0.01) * 50;
                    if (x === 0) ctx.moveTo(x, y);
                    else ctx.lineTo(x, y);
                }
                ctx.stroke();
            }
            ctx.restore();
        }

        // 增强鼠标移动事件
        document.addEventListener('mousemove', (e) => {
            mouse.x = e.clientX;
            mouse.y = e.clientY;

            // 更新自定义光标位置和大小
            const speed = Math.sqrt(e.movementX**2 + e.movementY**2);
            const scale = 1 + Math.min(speed * 0.1, 0.5);
            cursor.style.left = (e.clientX - 15) + 'px';
            cursor.style.top = (e.clientY - 15) + 'px';
            cursor.style.transform = `scale(${scale})`;

            // 根据移动速度创建粒子
            const particleChance = Math.min(speed * 0.05 + 0.3, 0.9);
            if (Math.random() < particleChance) {
                createParticles(e.clientX, e.clientY, Math.ceil(speed * 0.1) + 1);
            }
        });

        // 滚轮事件 - 控制粒子数量
        document.addEventListener('wheel', (e) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? -0.1 : 0.1;
            particleMultiplier = Math.max(0.1, Math.min(5.0, particleMultiplier + delta));

            // 滚轮位置创建特效
            createSpiral(e.clientX, e.clientY);
        });

        // 鼠标按下事件
        document.addEventListener('mousedown', (e) => {
            isMouseDown = true;
            createExplosion(e.clientX, e.clientY, 1.5);

            // 长按连续爆炸
            mouseDownTimer = setInterval(() => {
                if (isMouseDown) {
                    createExplosion(mouse.x + (Math.random() - 0.5) * 100,
                                  mouse.y + (Math.random() - 0.5) * 100, 0.8);
                }
            }, 100);
        });

        // 鼠标释放事件
        document.addEventListener('mouseup', () => {
            isMouseDown = false;
            if (mouseDownTimer) {
                clearInterval(mouseDownTimer);
                mouseDownTimer = null;
            }
        });

        // 双击事件 - 超级爆炸
        document.addEventListener('dblclick', (e) => {
            createExplosion(e.clientX, e.clientY, 3);
            // 创建多重冲击波
            setTimeout(() => createExplosion(e.clientX, e.clientY, 2), 200);
            setTimeout(() => createExplosion(e.clientX, e.clientY, 1), 400);
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            switch(e.key.toLowerCase()) {
                case ' ': // 空格键 - 清屏爆炸
                    e.preventDefault();
                    for (let i = 0; i < 10; i++) {
                        setTimeout(() => {
                            createExplosion(
                                Math.random() * canvas.width,
                                Math.random() * canvas.height,
                                2
                            );
                        }, i * 100);
                    }
                    break;
                case 'c': // C键 - 清除所有粒子
                    particles = [];
                    break;
                case 'r': // R键 - 重置倍率
                    particleMultiplier = 1;
                    break;
                case 's': // S键 - 螺旋效果
                    createSpiral(canvas.width/2, canvas.height/2);
                    break;
            }
        });

        // 全屏功能
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log('无法进入全屏模式:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }

        // 窗口大小改变事件
        window.addEventListener('resize', resizeCanvas);

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                cancelAnimationFrame(animationId);
            } else {
                animate();
            }
        });

        // 初始化
        resizeCanvas();
        animate();

        // 创建初始粒子效果
        setTimeout(() => {
            for (let i = 0; i < 20; i++) {
                createParticles(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    3
                );
            }
        }, 500);

        // 自动全屏（需要用户交互后才能生效）
        document.addEventListener('click', function autoFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log('自动全屏失败:', err);
                });
            }
            document.removeEventListener('click', autoFullscreen);
        }, { once: true });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>炫酷全屏交互动画</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            overflow: hidden;
            background: linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e);
            cursor: none;
        }

        #canvas {
            display: block;
            background: radial-gradient(circle at center, rgba(26, 26, 46, 0.8), rgba(10, 10, 10, 1));
        }

        .fullscreen-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-family: 'Arial', sans-serif;
            font-weight: bold;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .fullscreen-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: scale(1.05);
        }

        .cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            border: 2px solid #00ffff;
            border-radius: 50%;
            pointer-events: none;
            z-index: 999;
            transition: all 0.1s ease;
            box-shadow: 0 0 20px #00ffff, inset 0 0 20px #00ffff;
        }

        .info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            color: rgba(255, 255, 255, 0.7);
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <canvas id="canvas"></canvas>
    <div class="cursor" id="cursor"></div>
    <button class="fullscreen-btn" onclick="toggleFullscreen()">全屏</button>
    <div class="info">
        移动鼠标体验炫酷效果 | 点击产生爆炸效果
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const cursor = document.getElementById('cursor');

        let particles = [];
        let mouse = { x: 0, y: 0 };
        let animationId;

        // 设置画布大小
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }

        // 粒子类
        class Particle {
            constructor(x, y, vx = 0, vy = 0, life = 100) {
                this.x = x;
                this.y = y;
                this.vx = vx;
                this.vy = vy;
                this.life = life;
                this.maxLife = life;
                this.size = Math.random() * 3 + 1;
                this.color = this.getRandomColor();
                this.trail = [];
            }

            getRandomColor() {
                const colors = [
                    '#00ffff', '#ff00ff', '#ffff00', '#00ff00', 
                    '#ff6600', '#6600ff', '#ff0066', '#66ff00'
                ];
                return colors[Math.floor(Math.random() * colors.length)];
            }

            update() {
                // 添加轨迹点
                this.trail.push({ x: this.x, y: this.y });
                if (this.trail.length > 10) {
                    this.trail.shift();
                }

                // 受鼠标影响
                const dx = mouse.x - this.x;
                const dy = mouse.y - this.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                
                if (distance < 150) {
                    const force = (150 - distance) / 150;
                    this.vx += (dx / distance) * force * 0.5;
                    this.vy += (dy / distance) * force * 0.5;
                }

                // 更新位置
                this.x += this.vx;
                this.y += this.vy;

                // 添加阻力
                this.vx *= 0.98;
                this.vy *= 0.98;

                // 边界反弹
                if (this.x < 0 || this.x > canvas.width) this.vx *= -0.8;
                if (this.y < 0 || this.y > canvas.height) this.vy *= -0.8;

                // 生命值递减
                this.life--;
            }

            draw() {
                const alpha = this.life / this.maxLife;
                
                // 绘制轨迹
                ctx.strokeStyle = this.color + '30';
                ctx.lineWidth = 1;
                ctx.beginPath();
                for (let i = 0; i < this.trail.length; i++) {
                    const point = this.trail[i];
                    if (i === 0) {
                        ctx.moveTo(point.x, point.y);
                    } else {
                        ctx.lineTo(point.x, point.y);
                    }
                }
                ctx.stroke();

                // 绘制粒子
                ctx.save();
                ctx.globalAlpha = alpha;
                ctx.fillStyle = this.color;
                ctx.shadowBlur = 20;
                ctx.shadowColor = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
            }

            isDead() {
                return this.life <= 0;
            }
        }

        // 创建粒子
        function createParticles(x, y, count = 5) {
            for (let i = 0; i < count; i++) {
                const angle = (Math.PI * 2 * i) / count;
                const speed = Math.random() * 3 + 1;
                const vx = Math.cos(angle) * speed;
                const vy = Math.sin(angle) * speed;
                particles.push(new Particle(x, y, vx, vy, 150 + Math.random() * 100));
            }
        }

        // 创建爆炸效果
        function createExplosion(x, y) {
            for (let i = 0; i < 30; i++) {
                const angle = Math.random() * Math.PI * 2;
                const speed = Math.random() * 8 + 2;
                const vx = Math.cos(angle) * speed;
                const vy = Math.sin(angle) * speed;
                particles.push(new Particle(x, y, vx, vy, 100 + Math.random() * 50));
            }
        }

        // 动画循环
        function animate() {
            ctx.fillStyle = 'rgba(10, 10, 10, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 更新和绘制粒子
            particles = particles.filter(particle => {
                particle.update();
                particle.draw();
                return !particle.isDead();
            });

            // 持续创建新粒子
            if (Math.random() < 0.3) {
                createParticles(
                    Math.random() * canvas.width,
                    Math.random() * canvas.height,
                    1
                );
            }

            animationId = requestAnimationFrame(animate);
        }

        // 鼠标移动事件
        document.addEventListener('mousemove', (e) => {
            mouse.x = e.clientX;
            mouse.y = e.clientY;
            
            // 更新自定义光标位置
            cursor.style.left = (e.clientX - 10) + 'px';
            cursor.style.top = (e.clientY - 10) + 'px';

            // 在鼠标位置创建粒子
            if (Math.random() < 0.7) {
                createParticles(e.clientX, e.clientY, 2);
            }
        });

        // 点击事件
        document.addEventListener('click', (e) => {
            createExplosion(e.clientX, e.clientY);
        });

        // 全屏功能
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log('无法进入全屏模式:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }

        // 窗口大小改变事件
        window.addEventListener('resize', resizeCanvas);

        // 初始化
        resizeCanvas();
        animate();

        // 自动全屏（需要用户交互后才能生效）
        document.addEventListener('click', function autoFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log('自动全屏失败:', err);
                });
            }
            document.removeEventListener('click', autoFullscreen);
        }, { once: true });
    </script>
</body>
</html>
